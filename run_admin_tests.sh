#!/bin/bash

# Admin Tests Runner Script
# This script runs all admin-related tests for the Telegram bot

echo "🚀 Starting Admin Test Suite"
echo "============================"

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to run a test and report results
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    echo -e "\n${BLUE}🧪 Running: ${test_name}${NC}"
    echo "   Command: $test_command"
    
    if eval "$test_command"; then
        echo -e "   ${GREEN}✅ PASSED${NC}"
        return 0
    else
        echo -e "   ${RED}❌ FAILED${NC}"
        return 1
    fi
}

# Test categories
echo -e "\n${YELLOW}📋 Test Categories:${NC}"
echo "1. Database Operations Tests"
echo "2. Admin Step Parsing Tests"
echo "3. Input Validation Tests"
echo "4. Performance Tests"
echo "5. Integration Tests (with teloxide_tests)"

# Initialize counters
passed=0
failed=0

# Database Operations Tests
echo -e "\n${BLUE}🗄️  Database Operations Tests${NC}"
echo "================================"

if run_test "Model Creation Workflow" "cargo test --lib admin::tests::test_admin_model_creation_workflow -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

if run_test "Batch Link Upload" "cargo test --lib admin::tests::test_batch_link_upload -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

if run_test "Existing Model Management" "cargo test --lib admin::tests::test_existing_model_management -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

# Validation Tests
echo -e "\n${BLUE}✅ Input Validation Tests${NC}"
echo "=========================="

if run_test "Admin Step Parsing" "cargo test --lib admin::tests::test_admin_step_parsing -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

if run_test "Model Data Validation" "cargo test --lib admin::tests::test_model_data_validation -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

if run_test "Link Validation" "cargo test --lib admin::tests::test_link_validation -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

# Workflow Tests
echo -e "\n${BLUE}🔄 Complete Workflow Tests${NC}"
echo "==========================="

if run_test "Complete Admin Workflow Simulation" "cargo test --lib admin::tests::test_complete_admin_workflow_simulation -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

# Performance Tests
echo -e "\n${BLUE}⚡ Performance Tests${NC}"
echo "===================="

if run_test "Performance with Many Links" "cargo test --lib admin::tests::test_performance_with_many_links --release -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

# Integration Tests (if teloxide_tests feature is enabled)
echo -e "\n${BLUE}🤖 Integration Tests (MockBot)${NC}"
echo "==============================="

if run_test "Admin Command Response" "cargo test --lib admin::tests::integration_tests::test_admin_command_response --features teloxide_tests -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

if run_test "Model Creation Input Validation" "cargo test --lib admin::tests::integration_tests::test_model_creation_input_validation --features teloxide_tests -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

if run_test "Link Upload Functionality" "cargo test --lib admin::tests::integration_tests::test_link_upload_functionality --features teloxide_tests -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

if run_test "Admin Workflow Simulation (MockBot)" "cargo test --lib admin::tests::integration_tests::test_admin_workflow_simulation --features teloxide_tests -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

if run_test "Error Handling" "cargo test --lib admin::tests::integration_tests::test_error_handling --features teloxide_tests -- --nocapture"; then
    ((passed++))
else
    ((failed++))
fi

# Summary
echo -e "\n${YELLOW}📊 Test Results Summary${NC}"
echo "======================="
echo -e "✅ Passed: ${GREEN}$passed${NC}"
echo -e "❌ Failed: ${RED}$failed${NC}"
total=$((passed + failed))
echo -e "📈 Total: $total"

if [ $failed -eq 0 ]; then
    success_rate=100
else
    success_rate=$(( (passed * 100) / total ))
fi

echo -e "🎯 Success Rate: ${success_rate}%"

# Final result
echo -e "\n${YELLOW}🏁 Final Result${NC}"
echo "==============="

if [ $failed -eq 0 ]; then
    echo -e "${GREEN}🎉 All tests passed! The admin system is working correctly.${NC}"
    exit 0
else
    echo -e "${RED}⚠️  Some tests failed. Please check the error messages above.${NC}"
    exit 1
fi
