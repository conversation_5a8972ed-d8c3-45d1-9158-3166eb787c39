{"id": "692bf768-1830-4937-8b73-863de4b940d1", "rows_written": 183, "rows_read": 525, "storage_bytes_used": 94208, "write_requests_delegated": 0, "current_frame_no": 125, "top_query_threshold": 2, "top_queries": [{"rows_written": 0, "rows_read": 2, "query": "SELECT * FROM `users` ORDER BY `id` ASC LIMIT 100 OFFSET 0;"}, {"rows_written": 0, "rows_read": 2, "query": "SELECT * FROM purchases WHERE date > '2025-04-17' AND date >= ? AND date < ? ORDER BY date DESC;"}, {"rows_written": 1, "rows_read": 1, "query": "INSERT INTO attributes (KEY, value) VALUES (?, ?) ON CONFLICT (KEY) DO UPDATE SET value = excluded.value;"}, {"rows_written": 1, "rows_read": 1, "query": "INSERT INTO users (id, username, path, cart, balance, pending_invoice_id, is_admin, active_promocode_id, referred_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT DO UPDATE SET username = excluded.username, path = excluded.path, cart = excluded.cart, balance = excluded.balance, pending_invoice_id = excluded.pending_invoice_id, is_admin = excluded.is_admin, active_promocode_id = excluded.active_promocode_id, referred_by = excluded.referred_by;"}, {"rows_written": 1, "rows_read": 1, "query": "UPDATE users SET balance = ? WHERE \"id\" = ?;"}, {"rows_written": 1, "rows_read": 1, "query": "UPDATE users SET is_admin = ? WHERE \"id\" = ?;"}, {"rows_written": 0, "rows_read": 14, "query": "SELECT * FROM `catalog` ORDER BY `id` ASC LIMIT 100 OFFSET 0;"}, {"rows_written": 0, "rows_read": 14, "query": "SELECT id, media, description FROM catalog WHERE kind = 'category' AND nav IS NULL;"}, {"rows_written": 0, "rows_read": 22, "query": "SELECT name FROM sqlite_master WHERE type = 'view';"}, {"rows_written": 0, "rows_read": 35, "query": "SELECT name FROM sqlite_master WHERE type = 'table' ORDER BY name;"}], "slowest_query_threshold": 2, "slowest_queries": [{"elapsed_ms": 2, "query": "SELECT id, media, description FROM catalog WHERE kind = 'category' AND nav IS NULL;", "rows_written": 0, "rows_read": 14}, {"elapsed_ms": 2, "query": "SELECT media_file_id FROM uploaded_medias WHERE bot_username = ? AND media_name = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 2, "query": "SELECT name FROM sqlite_master WHERE type = 'table' ORDER BY name;", "rows_written": 0, "rows_read": 35}, {"elapsed_ms": 3, "query": "SELECT * FROM purchases WHERE date > '2025-04-17' AND date >= ? AND date < ? ORDER BY date DESC;", "rows_written": 0, "rows_read": 2}, {"elapsed_ms": 3, "query": "SELECT hash FROM migrations WHERE name = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 4, "query": "INSERT INTO users (id, username, path, cart, balance, pending_invoice_id, is_admin, active_promocode_id, referred_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT DO UPDATE SET username = excluded.username, path = excluded.path, cart = excluded.cart, balance = excluded.balance, pending_invoice_id = excluded.pending_invoice_id, is_admin = excluded.is_admin, active_promocode_id = excluded.active_promocode_id, referred_by = excluded.referred_by;", "rows_written": 1, "rows_read": 1}, {"elapsed_ms": 4, "query": "SELECT * FROM users WHERE id = ?;", "rows_written": 0, "rows_read": 1}, {"elapsed_ms": 6, "query": "INSERT INTO attributes (KEY, value) VALUES (?, ?) ON CONFLICT (KEY) DO UPDATE SET value = excluded.value;", "rows_written": 1, "rows_read": 1}, {"elapsed_ms": 9, "query": "CREATE TABLE IF NOT EXISTS migrations(name TEXT PRIMARY KEY, hash TEXT)", "rows_written": 3, "rows_read": 1}, {"elapsed_ms": 36, "query": "SELECT * FROM catalog WHERE nav IS NOT NULL;", "rows_written": 0, "rows_read": 14}], "embedded_replica_frames_replicated": 0, "query_count": 332, "query_latency": 346738}