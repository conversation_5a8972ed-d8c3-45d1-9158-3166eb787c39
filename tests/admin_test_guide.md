# Admin Integration Tests Guide

This guide explains how to run and understand the comprehensive admin tests for the Telegram bot's link management system.

## Overview

The admin tests are located in `src/tiposhop/admin.rs` in the `#[cfg(test)]` module. These tests simulate real admin interactions with the bot using a local SQLite database to ensure all database methods work correctly.

## Test Structure

### Test Categories

#### 1. **Model Creation Workflow** (`test_admin_model_creation_workflow`)
**Purpose**: Tests the complete model creation process without initial links

**What it tests**:
- Creating categories in database
- Creating products with link data in JSON field
- Verifying no initial links are created
- Validating product data storage

**Key assertions**:
- Product created with correct name, price, category
- Link data stored properly in JSON field
- No links exist initially (total=0, available=0)

#### 2. **Batch Link Upload** (`test_batch_link_upload`)
**Purpose**: Tests adding single and multiple links to products

**What it tests**:
- Adding single link to product
- Adding batch of multiple links
- Verifying FIFO ordering (oldest first)
- Testing link selling and availability updates

**Key assertions**:
- Link counts update correctly
- FIFO ordering maintained
- Sold links marked properly with timestamps

#### 3. **Admin Step Parsing** (`test_admin_step_parsing`)
**Purpose**: Tests AdminStep enum serialization/deserialization

**What it tests**:
- String conversion of admin steps
- Parsing admin steps from strings
- All admin step variants work correctly

**Key assertions**:
- Serialization/deserialization roundtrip works
- All step types parse correctly

#### 4. **Model Data Validation** (`test_model_data_validation`)
**Purpose**: Tests input validation for model creation

**What it tests**:
- Valid model input format (name + price)
- Invalid input rejection
- Price parsing validation

**Key assertions**:
- Valid inputs parse correctly
- Invalid inputs are rejected

#### 5. **Link Validation** (`test_link_validation`)
**Purpose**: Tests link parsing and filtering logic

**What it tests**:
- Valid HTTP/HTTPS links are accepted
- Invalid links are filtered out
- Empty lines are ignored

**Key assertions**:
- Only valid HTTP links are processed
- Invalid protocols are filtered out

#### 6. **Existing Model Management** (`test_existing_model_management`)
**Purpose**: Tests admin ability to manage links for existing models

**What it tests**:
- Creating multiple products with different categories
- Retrieving all link products (sorted by name)
- Adding links to different models
- Simulating sales across multiple models

**Key assertions**:
- All products retrieved correctly
- Links can be added to any existing model
- Independent link management per model

#### 7. **Complete Admin Workflow Simulation** (`test_complete_admin_workflow_simulation`)
**Purpose**: End-to-end simulation of real admin usage patterns

**What it tests**:
- Setup multiple categories
- Create first model and skip adding links
- Create second model and add links immediately
- Go back and add links to first model
- Simulate customer purchases
- Admin adds more links when running low
- Test search functionality
- Final verification of all data

**Key assertions**:
- Complete workflow executes without errors
- All database operations work correctly
- Link counts and availability accurate throughout
- Search functionality works with real data

#### 8. **Performance Testing** (`test_performance_with_many_links`)
**Purpose**: Tests system performance with large numbers of links

**What it tests**:
- Creating product with 1000 links
- Measuring batch insert performance
- Measuring link count query performance
- Measuring get available link performance
- Measuring mark sold performance

**Key assertions**:
- Batch insert completes within 5 seconds
- Count queries complete within 100ms
- Get link queries complete within 100ms
- Mark sold operations complete within 100ms

## Running the Tests

### Run All Admin Tests
```bash
cargo test --lib admin::tests
```

### Run Specific Test
```bash
cargo test --lib admin::tests::test_admin_model_creation_workflow
```

### Run with Output
```bash
cargo test --lib admin::tests -- --nocapture
```

### Run Tests in Release Mode (for performance testing)
```bash
cargo test --lib admin::tests --release
```

## Test Database Setup

Each test creates its own temporary SQLite database using `libsql`:

```rust
async fn create_test_db() -> Database {
    let temp_file = NamedTempFile::new().unwrap();
    let db_path = temp_file.path().to_str().unwrap();
    let db_url = format!("file:{}", db_path);
    Database::new(&db_url).await.unwrap()
}
```

This ensures:
- Complete isolation between tests
- No interference from existing data
- Clean state for each test
- Automatic cleanup after tests

## Test Data

Tests use realistic data that mirrors production usage:

- **Categories**: "premium-models", "basic-models", "exclusive-models"
- **Model Names**: "Premium Anna", "Basic Maria", etc.
- **Prices**: Realistic pricing ($19.99, $25.99, $49.99)
- **Links**: Valid HTTPS URLs with meaningful paths
- **Media**: Video and photo references

## Expected Output

When tests run successfully, you'll see output like:

```
🚀 Starting complete admin workflow simulation...
✅ Categories created
✅ First model created: abc123...
✅ Second model created with links: def456...
✅ Links added to first model
🛒 Simulating customer purchases...
✅ Customer purchases simulated
✅ Additional links added to Maria
✅ Search functionality verified
📊 Premium Anna: 5 total links, 4 available
📊 Basic Maria: 5 total links, 3 available
🎉 Complete admin workflow simulation passed!
```

## Key Features Tested

### ✅ **Streamlined Model Creation**
- No initial link requirement
- Optional immediate link addition
- Skip functionality for later link management

### ✅ **Flexible Link Management**
- Batch link upload with validation
- FIFO link assignment
- Real-time statistics tracking

### ✅ **Database Operations**
- Product creation and retrieval
- Link creation and management
- Search functionality
- Performance with large datasets

### ✅ **Error Handling**
- Invalid input validation
- Missing data handling
- Edge case management

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Ensure `libsql` is properly installed
   - Check file permissions for temporary directories

2. **Test Timeouts**
   - Performance tests have reasonable thresholds
   - Adjust timing assertions if running on slower hardware

3. **Assertion Failures**
   - Check that database schema matches expectations
   - Verify migration has been applied correctly

### Debug Mode

Run tests with debug output:
```bash
RUST_LOG=debug cargo test --lib admin::tests -- --nocapture
```

## Contributing

When adding new tests:

1. Follow the existing naming convention
2. Include comprehensive assertions
3. Add realistic test data
4. Document the test purpose and scenarios
5. Update this guide with new test descriptions

## Integration with CI/CD

These tests are designed to run in CI/CD environments:

- No external dependencies required
- Fast execution (< 30 seconds for all tests)
- Deterministic results
- Clear failure messages

Add to your CI pipeline:
```yaml
- name: Run Admin Tests
  run: cargo test --lib admin::tests --release
```
