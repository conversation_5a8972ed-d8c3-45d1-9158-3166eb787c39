# Link System Implementation Test Plan

## Overview
This document outlines the testing plan for the new separate link storage system.

## Changes Made

### Database Changes
1. **New `links` table**: Stores individual links separately from products
   - `id`: Primary key
   - `product_id`: Foreign key to products table
   - `link`: The actual link URL
   - `sold`: Boolean flag indicating if link has been sold
   - `created_at`: Timestamp when link was created
   - `sold_at`: Timestamp when link was sold (nullable)

2. **Modified `products` table**: Added `data` field for JSON storage
   - `data`: JSON field containing product-specific data (LinkProductData for link products)

3. **Removed `link_product_data` table**: Data now stored in products.data field

4. **Single migration**: All changes consolidated into one migration for development

### Admin Interface Changes
1. **New admin step**: `AddLinksToModel(ProductId)` for batch link upload
2. **Enhanced model creation flow**: After creating a model, admin can add more links
3. **Batch link upload**: Admin can paste multiple links (one per line)
4. **Link statistics**: Shows total and available link counts

### Checkout Process Changes
1. **Availability check**: Before checkout, verify links are available
2. **Link assignment**: Get oldest available link for each purchased product
3. **Mark as sold**: Update link's `sold` flag and `sold_at` timestamp
4. **Error handling**: Handle case when no links are available

## Database Methods Added
- `create_link(product_id, link)`: Create a single link
- `create_links_batch(product_id, links)`: Create multiple links
- `get_available_link(product_id)`: Get oldest unsold link
- `mark_link_sold(link_id)`: Mark link as sold
- `get_link_count(product_id)`: Get total and available counts

## Testing Scenarios

### 1. Model Creation with Links
- Admin creates a new model with initial link
- System should create product, link_product_data, and initial link
- Admin should see option to add more links

### 2. Batch Link Upload
- Admin clicks "Add Links" after model creation
- Admin pastes multiple links (one per line)
- System should validate and store all valid links
- Invalid links should be filtered out

### 3. Link Availability Check
- User adds product to cart
- During checkout, system checks if links are available
- If no links available, checkout should fail with error message

### 4. Link Assignment and Sale
- User successfully checks out with link product
- System should find oldest available link
- Link should be marked as sold with timestamp
- User should receive the specific link

### 5. Multiple Purchases
- Multiple users purchase same product
- Each should get different link
- Links should be assigned in FIFO order (oldest first)
- No link should be sold twice

## Expected Behavior

### Admin Flow
1. Create model → Success message with "Add Links" option
2. Click "Add Links" → Instructions for batch upload
3. Paste links → Validation and storage
4. Success message with statistics and option to add more

### User Flow
1. Add product to cart → Normal behavior
2. Checkout → Availability check, then normal payment flow
3. After payment → Receive specific link for purchased product
4. If no links available → Error message, payment blocked

## Migration Safety
- Existing links are preserved during migration
- Old `link_product_data` entries are migrated to new structure
- No data loss should occur
- System should work with both old and new data

## Error Handling
- Invalid links (not starting with http) are filtered out
- Empty link submissions show appropriate error
- No available links prevents checkout
- Database errors are logged and handled gracefully
