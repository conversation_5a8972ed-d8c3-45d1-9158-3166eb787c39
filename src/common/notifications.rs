use serde::{Deserialize, Serialize};

crate::db_enum!(
    pub enum NotificationKind {
        DbBought,
        ProxiesBought,
        TeateagramBought,
        ManualBought,
        SalePromocodeActivated,
        BalancePromocodeActivated,
        UserReferred,
        DailyReport,
        ModelAdded,
    }
);

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationConfig {
    pub kind: NotificationKind,
    pub channel_id: i64,
}
