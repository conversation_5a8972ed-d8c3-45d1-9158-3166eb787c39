use serde::{Deserialize, Serialize};

use crate::{db::Json, media::Media};

crate::id!(ProductId(String));

crate::id!(ProductInstanceId(i64), Copy);

crate::db_enum!(
    pub enum ProductKind {
        Link,
    }
);

#[derive(Clone, Debug, Deserialize)]
pub struct ProductCategory {
    pub id: String,
    pub media: String,
    pub description: Option<String>,
}

// product kind maps to how product data is stored in db (or else)
// how it is extracted, how it is displayed and everything else
// for links:
#[derive(Clone, Debug, Deserialize, Serialize)]
pub struct LinkProductData {
    pub model_name: String, // also search term
    pub medias: Vec<Media>,
    pub description: String,
}

// Individual link that can be sold
#[derive(Clone, Debug, Deserialize)]
pub struct Link {
    pub id: i64,
    pub product_id: ProductId,
    pub link: String,
    pub sold: bool,
    pub created_at: String,
    pub sold_at: Option<String>,
}

// even though model data is unique and it makes no sense to store it separately from product description,
// i will
// and you won't stop me
// Probably it's best to store them separately, because after we would
// add non-unique products

#[derive(Clone, Debug, Deserialize)]
pub struct ProductDescription {
    pub id: ProductId,
    pub category: Option<String>,
    pub kind: ProductKind,

    pub enabled: bool,

    pub name: String,
    pub price: f64,
    pub production_cost: f64,
    pub data: Option<Json<LinkProductData>>, // JSON field for product-specific data
}

impl std::hash::Hash for ProductDescription {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        self.id.hash(state);
    }
}

impl PartialEq for ProductDescription {
    fn eq(&self, other: &Self) -> bool {
        self.id == other.id
    }
}

impl Eq for ProductDescription {}
