mod ext;
mod macros;
mod migrations;

use std::sync::Arc;
use std::time::Duration;

use chrono::{DateTime, Utc};
use color_eyre::{eyre::Context as _, Report};
use libsql::{named_params, params};
use moka::future::Cache;

pub use ext::{FromValue, Json};
use tracing::instrument;

use crate::{
    common::{
        CatalogEntry, Link, LinkProductData, NotificationConfig, NotificationKind, ProductCategory,
        ProductId, ProductKind, Promocode, PromocodeId, PromocodeKind, Purchase, User, UserId,
    },
    media::Media,
    ProductDescription,
};

#[derive(Clone)]
pub struct Db {
    inner: Arc<DbInner>,
}

pub struct DbInner {
    db: libsql::Database,
    conn: Cache<(), libsql::Connection>,
    notification_config_cache: Cache<NotificationKind, NotificationConfig>,
}

impl std::ops::Deref for Db {
    type Target = DbInner;

    fn deref(&self) -> &Self::Target {
        &self.inner
    }
}

impl Db {
    #[instrument]
    pub async fn connect_remote(url: String) -> Result<Db, Report> {
        let db = libsql::Builder::new_remote(url, "basic:dXNlcjpwYXNz".to_owned())
            .build()
            .await?;
        let conn = db.connect()?;

        migrations::migrate(&conn).await?;

        Ok(Db {
            inner: Arc::new(DbInner {
                db,
                conn: Cache::builder()
                    .time_to_idle(Duration::from_secs(10))
                    .build(),
                notification_config_cache: Cache::builder()
                    .time_to_live(Duration::from_secs(300)) // 5 minutes
                    .build(),
            }),
        })
    }
}

impl DbInner {
    #[instrument(skip_all)]
    pub async fn get_attribute(&self, key: &str) -> Result<Option<String>, Report> {
        self.query_opt_col("SELECT value FROM attributes WHERE key = ?", &[key])
            .await
            .wrap_err("Failed to get attribute")
    }

    #[instrument(skip_all)]
    pub async fn set_attribute(&self, key: &str, value: &str) -> Result<(), Report> {
        self.execute(
            "INSERT INTO attributes (key, value) VALUES (?, ?) ON CONFLICT(key) DO UPDATE SET value = excluded.value",
            &[key, value],
        )
        .await
        .wrap_err("Failed to set attribute")
    }

    #[instrument(skip_all)]
    pub async fn get_user(&self, id: UserId) -> Result<User, Report> {
        let maybe_user = self
            .query_opt_row("SELECT * FROM users WHERE id = ?", &[id])
            .await
            .wrap_err("Failed to get user from db")?;

        if let Some(user) = maybe_user {
            return Ok(user);
        }

        // User doesn't exist, create a default user
        let default_user = User {
            id,
            username: None,
            cart: Default::default(),
            path: Default::default(),
            balance: 0.,
            pending_invoice_id: None,
            is_admin: false,
            active_promocode_id: None,
            referred_by: None,
            first_time: true,
        };

        // Save the new user to the database
        tracing::debug!("User doesn't exist yet, creating and saving default user");
        self.save_user(&default_user).await?;

        Ok(default_user)
    }

    #[instrument(skip_all)]
    pub async fn get_all_users(&self) -> Result<Vec<User>, Report> {
        self.query("SELECT * FROM users", ())
            .await
            .wrap_err("Failed to get all users")
    }

    #[instrument(skip_all)]
    pub async fn save_user(&self, user: &User) -> Result<(), Report> {
        tracing::info!("Saving user to db: {:?}", user);
        self.execute(
            "--sql
                INSERT INTO users (
                    id,
                    username,
                    path,
                    cart,
                    balance,
                    pending_invoice_id,
                    is_admin,
                    active_promocode_id,
                    referred_by
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ON CONFLICT DO
                    UPDATE SET
                        username=excluded.username,
                        path=excluded.path,
                        cart=excluded.cart,
                        balance=excluded.balance,
                        pending_invoice_id=excluded.pending_invoice_id,
                        is_admin=excluded.is_admin,
                        active_promocode_id=excluded.active_promocode_id,
                        referred_by=excluded.referred_by
                ",
            libsql::params!(
                user.id,
                user.username.as_deref(),
                user.path.clone(),
                user.cart.clone(),
                user.balance,
                user.pending_invoice_id,
                user.is_admin,
                user.active_promocode_id.clone(),
                user.referred_by,
            ),
        )
        .await
        .wrap_err("Failed to save user")?;

        tracing::info!("User saved to db");

        Ok(())
    }

    pub async fn get_product_category(
        &self,
        name: &str,
    ) -> Result<Option<ProductCategory>, Report> {
        self.query_opt_row(
            "SELECT * FROM catalog WHERE id = ? AND nav IS NULL LIMIT 1",
            params!(name),
        )
        .await
        .wrap_err("Failed to get product categories")
    }

    pub async fn get_all_product_categories(&self) -> Result<Vec<ProductCategory>, Report> {
        self.query(
            "SELECT id, media, description FROM catalog WHERE kind = 'category' AND nav IS NULL",
            (),
        )
        .await
        .wrap_err("Failed to get all product categories")
    }

    pub async fn get_products_by_category(
        &self,
        category: &str,
    ) -> Result<Vec<ProductDescription>, Report> {
        self.query(
            "SELECT * FROM products WHERE category = ? AND enabled",
            params!(category),
        )
        .await
        .wrap_err("Failed to get products by category")
    }

    pub async fn search_products(&self, query: &str) -> Result<Vec<ProductDescription>, Report> {
        // Search in product names and model names from JSON data field
        let search_pattern = format!("%{}%", query);
        self.query(
            "SELECT * FROM products
             WHERE enabled AND (
                 name LIKE ? COLLATE NOCASE OR
                 json_extract(data, '$.model_name') LIKE ? COLLATE NOCASE
             )
             ORDER BY name
             LIMIT 10",
            params!(search_pattern.clone(), search_pattern),
        )
        .await
        .wrap_err("Failed to search products")
    }

    pub async fn get_catalog(&self) -> Result<Vec<CatalogEntry>, Report> {
        self.query("SELECT * FROM catalog WHERE nav IS NOT NULL", ())
            .await
    }

    #[instrument(skip_all)]
    pub async fn get_product_description(
        &self,
        id: &ProductId,
    ) -> Result<Option<ProductDescription>, Report> {
        self.query_opt_row("SELECT * FROM products WHERE id = ? AND enabled", &[id])
            .await
            .wrap_err("Failed to get product row")
    }

    #[instrument(skip_all)]
    pub async fn get_file_id(
        &self,
        bot_username: &str,
        media_name: &str,
    ) -> Result<Option<String>, Report> {
        self.query_opt_col(
            "SELECT media_file_id FROM uploaded_medias WHERE bot_username=? AND media_name=?",
            &[bot_username, media_name],
        )
        .await
        .wrap_err("Failed to get file id")
    }

    #[instrument(skip_all)]
    pub async fn set_file_id(
        &self,
        bot_username: &str,
        media_name: &str,
        file_id: &str,
    ) -> Result<(), Report> {
        self.execute(
            "INSERT OR REPLACE INTO uploaded_medias (bot_username, media_name, media_file_id) VALUES (?, ?, ?)",
            &[bot_username, media_name, file_id],
        )
        .await
        .wrap_err("Failed to set file id")
    }

    #[instrument(skip_all)]
    pub async fn get_media_file_id(
        &self,
        bot_username: &str,
        media_name: &str,
    ) -> Result<Option<String>, Report> {
        self.get_file_id(bot_username, media_name).await
    }

    #[allow(clippy::too_many_arguments)]
    #[instrument(skip_all)]
    pub async fn register_purchase(
        &self,
        user_id: UserId,
        product_id: &ProductId,
        promo_id: Option<PromocodeId>,
        referred_by: Option<UserId>,
        referral_percent: Option<f64>,
        paid: f64,
        production_cost: f64,
    ) -> Result<(), Report> {
        self.execute(
            "INSERT INTO purchases (user_id, product_id, paid, date, promo_id, referred_by, referral_percent, production_cost)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            params!(
                user_id,
                product_id,
                paid,
                Utc::now().to_string(),
                promo_id,
                referred_by,
                referral_percent,
                production_cost
            ),
        )
        .await
        .wrap_err("Failed to register purchase")
    }

    #[instrument(skip_all)]
    pub async fn get_user_total_spent(&self, user_id: UserId) -> Result<f64, Report> {
        self.query_opt_col(
            "SELECT SUM(paid) FROM purchases WHERE user_id = ?",
            params!(user_id,),
        )
        .await
        .wrap_err("Failed to get user total spent")
        .map(|x| x.unwrap_or(0.))
    }

    #[instrument(skip_all)]
    pub async fn get_active_promo(&self, user_id: UserId) -> Result<Option<Promocode>, Report> {
        self.query_opt_row(
            "SELECT promocodes.* FROM promocodes
            JOIN users ON promocodes.id = users.active_promocode_id
            WHERE users.id = ? AND users.active_promocode_id IS NOT NULL AND datetime('now') > start_date AND datetime('now') < end_date",
            params!(user_id),
        )
        .await
        .wrap_err("Failed to get active promo")
    }

    #[instrument(skip_all)]
    pub async fn activate_promocode_by_id(
        &self,
        user_id: UserId,
        promocode_id: PromocodeId,
    ) -> Result<PromocodeActivation, Report> {
        tracing::debug!("Activating promocode {promocode_id}");

        if let Some(promo) = self.get_active_promo(user_id).await? {
            tracing::debug!("User already has active promocode {promo}");

            if promo.id == promocode_id {
                tracing::debug!("Promocode is already active");

                return Ok(PromocodeActivation::AlreadyActive(promo));
            }
        }

        let promocode_id = if let Some(promocode_id) = self
            .query_opt_col(
                "SELECT id FROM promocodes WHERE id = $1 OR old_id = $1",
                named_params!("1": &promocode_id),
            )
            .await
            .wrap_err("Failed to get promocode id")?
        {
            promocode_id
        } else {
            promocode_id
        };

        if self
            .query_one_col(
                "SELECT COUNT(*) > 0 FROM promocode_activations WHERE user_id = ? AND promo_id = ?",
                params!(user_id, &promocode_id),
            )
            .await
            .wrap_err("Failed to check promocode activations")?
        {
            tracing::debug!("Promocode was already activated in the past");
            return Ok(PromocodeActivation::ActivatedInThePast);
        }

        #[derive(serde::Deserialize)]
        struct Entry {
            start: bool,
            end: bool,
        }

        let bounds = self
            .query_row::<Entry>(
                "SELECT (datetime('now') > start_date) as 'start', (datetime('now') < end_date) as 'end' FROM promocodes WHERE id = ?",
                params!(&promocode_id),
            )
            .await
            .wrap_err("Failed to get promocode")?;

        if !bounds.start || !bounds.end {
            tracing::debug!("Promocode is not active in this time period");
            return Ok(PromocodeActivation::TimeBound);
        }

        let Some(promocode) = self
            .query_opt_row::<Promocode>(
                "UPDATE promocodes SET activations = activations + 1
                WHERE id = ? AND (activations_max IS NULL OR activations + 1 <= activations_max)
                RETURNING *",
                params!(&promocode_id),
            )
            .await
            .wrap_err("Failed to activate promocode")?
        else {
            tracing::debug!("Promocode is not active");
            return Ok(PromocodeActivation::NotFound);
        };

        self.execute(
            "INSERT INTO promocode_activations (user_id, promo_id, date) VALUES (?, ?, ?)",
            params!(user_id, &promocode_id, Utc::now().to_string()),
        )
        .await
        .wrap_err("Failed to activate promocode")?;
        tracing::debug!("Promocode activated successfully");

        Ok(PromocodeActivation::Activated(promocode))
    }

    #[instrument(skip_all)]
    pub async fn get_active_promocodes(&self) -> Result<Vec<Promocode>, Report> {
        self.query(
            "SELECT * FROM promocodes WHERE datetime('now') < end_date",
            (),
        )
        .await
        .wrap_err("Failed to get active promocodes")
    }

    #[instrument(skip_all)]
    pub async fn add_balance(&self, buyer_id: UserId, balance: f64) -> Result<(), Report> {
        tracing::debug!("Adding balance: {buyer_id} {balance}");
        self.execute(
            "UPDATE users
            SET balance = balance + ?
            WHERE id = ?",
            params!(balance, buyer_id),
        )
        .await
        .wrap_err("Failed to add balance")
    }

    #[instrument(skip_all)]
    pub async fn add_promocode(
        &self,
        text: String,
        value: i32,
        kind: PromocodeKind,
        activations: Option<i32>,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> Result<(), Report> {
        self.execute(
            "INSERT INTO promocodes (text, value, kind, start_date, end_date, activations_max) VALUES (?, ?, ?, ?, ?, ?)",
            params!(text, value, kind, start_date.to_string(), end_date.to_string(), activations),
        )
        .await
        .wrap_err("Failed to add promocode")
    }

    #[instrument(skip_all)]
    pub async fn create_product(
        &self,
        name: String,
        category: String,
        price: f64,
        data: Option<Json<LinkProductData>>,
    ) -> Result<ProductId, Report> {
        let product_id = ProductId(uuid::Uuid::new_v4().to_string());

        let data_json = match data {
            Some(d) => Some(serde_json::to_string(&d.inner()).unwrap()),
            None => None,
        };

        self.execute(
            "INSERT INTO products (id, name, kind, category, price, enabled, production_cost, data) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            libsql::params!(&product_id, name, ProductKind::Link, category, price, true, 0., data_json),
        )
        .await
        .wrap_err("Failed to create product")?;

        Ok(product_id)
    }

    #[instrument(skip_all)]
    pub async fn create_link(&self, product_id: ProductId, link: String) -> Result<(), Report> {
        self.execute(
            "INSERT INTO links (product_id, link) VALUES (?, ?)",
            params!(product_id, link),
        )
        .await
        .wrap_err("Failed to create link")
    }

    #[instrument(skip_all)]
    pub async fn create_links_batch(
        &self,
        product_id: ProductId,
        links: Vec<String>,
    ) -> Result<(), Report> {
        for link in links {
            self.execute(
                "INSERT INTO links (product_id, link) VALUES (?, ?)",
                params!(product_id.clone(), link),
            )
            .await
            .wrap_err("Failed to create link in batch")?;
        }
        Ok(())
    }

    #[instrument(skip_all)]
    pub async fn get_available_link(&self, product_id: &ProductId) -> Result<Option<Link>, Report> {
        self.query_opt_row(
            "SELECT * FROM links WHERE product_id = ? AND sold = FALSE ORDER BY created_at ASC LIMIT 1",
            &[product_id],
        )
        .await
        .wrap_err("Failed to get available link")
    }

    #[instrument(skip_all)]
    pub async fn mark_link_sold(&self, link_id: i64) -> Result<(), Report> {
        self.execute(
            "UPDATE links SET sold = TRUE, sold_at = datetime('now') WHERE id = ?",
            params!(link_id),
        )
        .await
        .wrap_err("Failed to mark link as sold")
    }

    #[instrument(skip_all)]
    pub async fn get_link_count(&self, product_id: &ProductId) -> Result<(i64, i64), Report> {
        let total: i64 = self
            .query_opt_col(
                "SELECT COUNT(*) FROM links WHERE product_id = ?",
                params!(product_id),
            )
            .await
            .wrap_err("Failed to get total link count")?
            .unwrap_or(0);

        let available: i64 = self
            .query_opt_col(
                "SELECT COUNT(*) FROM links WHERE product_id = ? AND sold = FALSE",
                params!(product_id),
            )
            .await
            .wrap_err("Failed to get available link count")?
            .unwrap_or(0);

        Ok((total, available))
    }

    #[instrument(skip_all)]
    pub async fn get_notification_config(
        &self,
        kind: NotificationKind,
    ) -> Result<Option<NotificationConfig>, Report> {
        // Try to get from cache first
        if let Some(config) = self.notification_config_cache.get(&kind).await {
            return Ok(Some(config.clone()));
        }

        // If not in cache, get from database
        let config: Option<NotificationConfig> = self
            .query_opt_row(
                "SELECT * FROM notification_configs WHERE kind = ?",
                params!(kind),
            )
            .await
            .wrap_err("Failed to get notification config")?;

        // If found, cache it
        if let Some(config) = &config {
            self.notification_config_cache
                .insert(kind, config.clone())
                .await;
        }

        Ok(config)
    }

    #[instrument(skip_all)]
    pub async fn get_users_referred(&self, user_id: UserId) -> Result<i32, Report> {
        self.query_one_col(
            "SELECT COUNT(*) FROM users WHERE reffered_by = ?",
            params!(user_id),
        )
        .await
        .wrap_err("Failed to get users referred")
    }

    #[instrument(skip_all)]
    pub async fn get_user_referral_income(&self, user_id: UserId) -> Result<f64, Report> {
        self.query_opt_col(
            "SELECT SUM(paid * (purchases.referral_percent / 100.0)) FROM purchases
            WHERE referred_by = ? AND referral_percent IS NOT NULL",
            params!(user_id),
        )
        .await
        .wrap_err("Failed to get user referral income")
        .map(|x| x.unwrap_or(0.))
    }

    #[instrument(skip_all)]
    pub async fn get_purchases_by_date_range(
        &self,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> Result<Vec<Purchase>, Report> {
        self.query(
            "SELECT * FROM purchases
            WHERE date > '2025-04-17' AND date >= ? AND date < ?
            ORDER BY date DESC",
            params!(start_date.to_string(), end_date.to_string()),
        )
        .await
        .wrap_err("Failed to get purchases by date range")
    }

    #[instrument(skip_all)]
    pub async fn store_associated_message(
        &self,
        primary_message_id: teloxide::types::MessageId,
        user_id: UserId,
        associated_message_id: teloxide::types::MessageId,
    ) -> Result<(), Report> {
        self.execute(
            "INSERT INTO associated_messages (primary_message_id, user_id, associated_message_id) VALUES (?, ?, ?)",
            params!(primary_message_id.0, user_id.0, associated_message_id.0),
        )
        .await
        .wrap_err("Failed to store associated message")
    }

    #[instrument(skip_all)]
    pub async fn get_associated_messages(
        &self,
        primary_message_id: teloxide::types::MessageId,
        user_id: UserId,
    ) -> Result<Vec<teloxide::types::MessageId>, Report> {
        let message_ids: Vec<i64> = self
            .query_col(
                "SELECT associated_message_id FROM associated_messages WHERE primary_message_id = ? AND user_id = ?",
                params!(primary_message_id.0, user_id.0),
            )
            .await
            .wrap_err("Failed to get associated messages")?;

        Ok(message_ids
            .into_iter()
            .map(|id| teloxide::types::MessageId(id as i32))
            .collect())
    }

    #[instrument(skip_all)]
    pub async fn delete_associated_messages(
        &self,
        primary_message_id: teloxide::types::MessageId,
        user_id: UserId,
    ) -> Result<(), Report> {
        self.execute(
            "DELETE FROM associated_messages WHERE primary_message_id = ? AND user_id = ?",
            params!(primary_message_id.0, user_id.0),
        )
        .await
        .wrap_err("Failed to delete associated messages")
    }
}

pub enum PromocodeActivation {
    Activated(Promocode),
    NotFound,
    ActivatedInThePast,
    TimeBound,
    AlreadyActive(Promocode),
}
