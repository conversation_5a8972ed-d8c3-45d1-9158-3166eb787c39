use std::{
    collections::{HashMap, HashSet},
    sync::Arc,
};

use chrono::{FixedOffset, Utc};

use crate::{
    common::{
        NotificationKind, ProductDescription, ProductId, Promocode, PromocodeKind, Purchase, UserId,
    },
    db::Db,
};

pub enum UserIdOrUsername {
    UserId(UserId),
    Username(String),
}

impl From<&str> for UserIdOrUsername {
    fn from(value: &str) -> Self {
        UserIdOrUsername::Username(value.to_owned())
    }
}

impl From<UserId> for UserIdOrUsername {
    fn from(value: UserId) -> Self {
        UserIdOrUsername::UserId(value)
    }
}

pub enum Notification {
    DbBought {
        id: UserIdOrUsername,
        products: HashSet<ProductDescription>,
        db: Db,
        promo: Option<Promocode>,
        referral: Option<UserId>,
    },
    PromocodeActivated {
        id: UserIdOrUsername,
        promo: Promocode,
    },
    UserReferred {
        id: UserIdOrUsername,
        referral: UserId,
    },
    DailyReport,
    ModelAdded {
        product_id: ProductId,
        model_name: String,
        description: String,
        medias: Vec<crate::media::Media>,
        bot_username: String,
    },
}

#[derive(Clone)]
pub struct NotificationClient {
    inner: Arc<NotificationClientInner>,
}

pub struct NotificationClientInner {
    pub default_channel_id: i64,
    pub client: reqwest::Client,
    pub bot_token: String,
    pub db: Db,
}

impl std::ops::Deref for NotificationClient {
    type Target = NotificationClientInner;

    fn deref(&self) -> &Self::Target {
        &self.inner
    }
}

impl NotificationClient {
    pub fn new(default_channel_id: i64, db: Db, bot_token: String) -> Self {
        NotificationClient {
            inner: Arc::new(NotificationClientInner {
                default_channel_id,
                client: reqwest::Client::new(),
                bot_token,
                db,
            }),
        }
    }

    pub fn run_reports_loop(&self) {
        let client = self.inner.clone();
        tokio::spawn(async move {
            const DAILY_REPORT_KEY: &str = "last_daily_report_date";
            const REPORT_HOUR: u32 = 0;
            const MOSCOW_OFFSET: FixedOffset = FixedOffset::east_opt(3 * 3600).unwrap();

            loop {
                let now = chrono::Utc::now().with_timezone(&MOSCOW_OFFSET);
                let today = now.naive_local();

                let last_report_date = client
                    .db
                    .get_attribute(DAILY_REPORT_KEY)
                    .await
                    .unwrap_or(None);
                let last_report_date = last_report_date
                    .and_then(|date| chrono::NaiveDate::parse_from_str(&date, "%Y-%m-%d").ok());

                let should_send_report = match last_report_date {
                    Some(last_report_date) => today.date() > last_report_date,
                    None => true,
                };

                if should_send_report {
                    tracing::info!("Sending daily report at {}", now);

                    client.send(Notification::DailyReport);

                    if let Err(e) = client
                        .db
                        .set_attribute(DAILY_REPORT_KEY, &now.date_naive().to_string())
                        .await
                    {
                        tracing::error!("Failed to update last report date: {}", e);
                    } else {
                        tracing::info!("Daily report sent successfully");
                    }
                }

                let next_report_time = today
                    .date()
                    .succ_opt()
                    .unwrap()
                    .and_hms_opt(REPORT_HOUR, 0, 0)
                    .unwrap();

                let sleep_duration = next_report_time - now.naive_local();
                tokio::time::sleep(sleep_duration.to_std().unwrap()).await;
            }
        });
    }
}

impl NotificationClientInner {
    pub fn send(&self, kind: Notification) {
        // Handle ModelAdded notifications separately since they need media groups
        if let Notification::ModelAdded { .. } = kind {
            self.send_model_added(kind);
            return;
        }

        let client = self.client.clone();
        let url = format!("https://api.telegram.org/bot{}/sendMessage", self.bot_token);
        let default_channel_id = self.default_channel_id;
        let db = self.db.clone();

        // Get the notification config kind based on the notification kind
        let config_kind = match kind {
            Notification::DbBought { .. } => NotificationKind::DbBought,
            Notification::PromocodeActivated {
                promo:
                    Promocode {
                        kind: PromocodeKind::Sale,
                        ..
                    },
                ..
            } => NotificationKind::SalePromocodeActivated,
            Notification::PromocodeActivated {
                promo:
                    Promocode {
                        kind: PromocodeKind::Balance,
                        ..
                    },
                ..
            } => NotificationKind::BalancePromocodeActivated,
            Notification::UserReferred { .. } => NotificationKind::UserReferred,
            Notification::DailyReport => NotificationKind::DailyReport,
            Notification::ModelAdded { .. } => NotificationKind::ModelAdded,
        };

        tokio::spawn(async move {
            let mut text = String::new();
            match kind {
                Notification::DailyReport => {
                    #[derive(Default)]
                    struct CategoryReport {
                        count: u32,
                        production_cost: f64,
                        revenue: f64,
                    }

                    impl CategoryReport {
                        fn profit(&self) -> f64 {
                            self.revenue - self.production_cost
                        }
                    }

                    #[derive(Default)]
                    struct TotalReport {
                        links: CategoryReport,
                        // a_ru: CategoryReport,
                        // a_eu: CategoryReport,
                        // insta: CategoryReport,
                        // vk: CategoryReport,
                        // ttg: CategoryReport,
                        // tvk: CategoryReport,
                        // proxies: CategoryReport,
                        // manuals: CategoryReport,
                        // teateagram: CategoryReport,
                    }

                    impl TotalReport {
                        fn profit(&self) -> f64 {
                            self.links.profit()
                            // self.a_ru.profit()
                            //     + self.a_eu.profit()
                            //     + self.vk.profit()
                            //     + self.insta.profit()
                            //     + self.ttg.profit()
                            //     + self.tvk.profit()
                            //     + self.proxies.profit()
                            //     + self.manuals.profit()
                            //     + self.teateagram.profit()
                        }

                        fn total_revenue(&self) -> f64 {
                            self.links.revenue
                            // self.a_ru.revenue
                            //     + self.a_eu.revenue
                            //     + self.vk.revenue
                            //     + self.insta.revenue
                            //     + self.ttg.revenue
                            //     + self.tvk.revenue
                            //     + self.proxies.revenue
                            //     + self.manuals.revenue
                            //     + self.teateagram.revenue
                        }
                    }

                    fn construct_report(purchases: Vec<Purchase>) -> TotalReport {
                        let mut report = TotalReport::default();
                        for purchase in purchases {
                            // let product_id = purchase.product_id.0;
                            // let cat = if product_id.starts_with("a_ru_") {
                            //     &mut report.a_ru
                            // } else if product_id.starts_with("a_eu_") {
                            //     &mut report.a_eu
                            // } else if product_id.starts_with("vk_") {
                            //     &mut report.vk
                            // } else if product_id.starts_with("inst_") {
                            //     &mut report.insta
                            // } else if product_id.starts_with("ttg_") {
                            //     &mut report.ttg
                            // } else if product_id.starts_with("tvk_") {
                            //     &mut report.tvk
                            // } else if product_id.starts_with("teateagram-") {
                            //     &mut report.teateagram
                            // } else if product_id.starts_with("man_") {
                            //     &mut report.manuals
                            // } else {
                            //     &mut report.proxies
                            // };

                            let cat = &mut report.links;

                            cat.count += purchase.amount;
                            cat.production_cost +=
                                purchase.production_cost * purchase.amount as f64;
                            cat.revenue += purchase.paid;
                        }

                        report
                    }

                    let now = Utc::now();
                    let yesterday = now.checked_sub_days(chrono::Days::new(1)).unwrap();
                    let purchases = match db.get_purchases_by_date_range(yesterday, now).await {
                        Ok(purchases) => purchases,
                        Err(e) => {
                            panic!("Failed to get purchases for daily report: {e}");
                        }
                    };

                    let day_report = construct_report(purchases);

                    text.push_str(&format!(
                        "📊 <b>Отчет {}</b>\n\n",
                        now.date_naive().format("%d.%m.%Y")
                    ));

                    // text.push_str(&format!(
                    //     "Telegram (Россия) - {} шт / {:.2}$\n",
                    //     day_report.a_ru.count,
                    //     day_report.a_ru.profit(),
                    // ));

                    // text.push_str(&format!(
                    //     "Telegram (Европа) - {} шт / {:.2}$\n",
                    //     day_report.a_eu.count,
                    //     day_report.a_eu.profit(),
                    // ));

                    // text.push_str(&format!(
                    //     "ВКонтакте (Россия) - {} шт / {:.2}$\n",
                    //     day_report.vk.count,
                    //     day_report.vk.profit(),
                    // ));

                    // text.push_str(&format!(
                    //     "Instagram (Россия) - {} шт / {:.2}$\n",
                    //     day_report.insta.count,
                    //     day_report.insta.profit(),
                    // ));

                    // text.push_str(&format!(
                    //     "Трафик (Телеграм) - {} шт / {:.2}$\n",
                    //     day_report.ttg.count,
                    //     day_report.ttg.profit(),
                    // ));

                    // text.push_str(&format!(
                    //     "Трафик (ВКонтакте) - {} шт / {:.2}$\n",
                    //     day_report.tvk.count,
                    //     day_report.tvk.profit(),
                    // ));

                    // text.push_str(&format!(
                    //     "Накрутка - {} шт / {:.2}$\n",
                    //     day_report.teateagram.count,
                    //     day_report.teateagram.profit(),
                    // ));

                    // text.push_str(&format!(
                    //     "Мануалы - {} шт / {:.2}$\n",
                    //     day_report.manuals.count,
                    //     day_report.manuals.profit(),
                    // ));

                    // text.push_str(&format!(
                    //     "Прокси - {} шт / {:.2}$\n",
                    //     day_report.proxies.count,
                    //     day_report.proxies.profit(),
                    // ));

                    text.push_str(&format!(
                        "Ссылки - {} шт / {:.2}$\n",
                        day_report.links.count,
                        day_report.links.profit(),
                    ));

                    text.push('\n');
                    text.push_str(&format!(
                        "За сегодня (ОБ): {:.2}$\n",
                        day_report.total_revenue()
                    ));
                    text.push_str(&format!("За сегодня: {:.2}$\n", day_report.profit()));

                    let week = now.checked_sub_days(chrono::Days::new(7)).unwrap();
                    let weekly = match db.get_purchases_by_date_range(week, now).await {
                        Ok(purchases) => purchases,
                        Err(e) => {
                            panic!("Failed to get purchases for weekly report: {e}");
                        }
                    };
                    let week_report = construct_report(weekly);

                    text.push_str(&format!("За неделю: {:.2}$\n", week_report.profit()));

                    let month = now.checked_sub_days(chrono::Days::new(30)).unwrap();
                    let monthly = match db.get_purchases_by_date_range(month, now).await {
                        Ok(purchases) => purchases,
                        Err(e) => {
                            panic!("Failed to get purchases for monthly report: {e}");
                        }
                    };
                    let month_report = construct_report(monthly);

                    text.push_str(&format!("За месяц: {:.2}$\n", month_report.profit()));
                }
                Notification::DbBought {
                    id,
                    products,
                    db,
                    promo,
                    referral,
                } => {
                    match id {
                        UserIdOrUsername::UserId(user_id) => {
                            text.push_str(&format!("🦍<a href=\"https://web.telegram.org/k/#{user_id}\">Пользователь</a> без юзернейма\n"));
                        }
                        UserIdOrUsername::Username(username) => {
                            text.push_str(&format!("🦍Пользователь : @{username}\n"));
                        }
                    }

                    text.push_str("🛒Купил:\n");

                    for (idx, product) in products.into_iter().enumerate() {
                        text.push_str(&format!("{} - ", idx + 1));
                        match product.kind {
                            crate::common::ProductKind::Link => {
                                text.push_str(&format!(
                                    "Модель: <i>{}</i> <b>${:.2}</b>\n",
                                    product.name, product.price
                                ));
                            } // crate::common::DbProductKind::AccountTg => text.push_str(&format!(
                              //     "Аккаунт Тг: <i>{}</i> <b>{amount} шт</b> (🌎{in_stock})\n",
                              //     product.name
                              // )),
                              // crate::common::DbProductKind::TrafficTg => text.push_str(&format!(
                              //     "БАЗА Тг: {amount} юзернеймов (🌎{in_stock})\n"
                              // )),
                              // crate::common::DbProductKind::TrafficVk => text.push_str(&format!(
                              //     "БАЗА ВК: {amount} юзернеймов (🌎{in_stock})\n"
                              // )),
                              // crate::common::DbProductKind::AccountVk => text.push_str(&format!(
                              //     "Аккаунт ВК: <i>{}</i> <b>{amount} шт</b> аккаунтов (🌎{in_stock})\n", product.name
                              // )),
                              // crate::common::DbProductKind::AccountInsta => text.push_str(&format!(
                              //     "Аккаунт Инста: <i>{}</i> <b>{amount} шт</b> (🌎{in_stock})\n", product.name
                              // )),
                        }
                    }

                    if let Some(promo) = promo {
                        text.push_str(&format!(
                            "\nПромокод: {} (-{}%)\n\n",
                            promo.text, promo.value
                        ));
                    }

                    if let Some(referral) = referral {
                        text.push_str(&format!("Рефералка: {referral}"));
                    }
                }

                Notification::PromocodeActivated { id, promo } => {
                    match id {
                        UserIdOrUsername::UserId(user_id) => {
                            text.push_str(&format!("🦍<a href=\"https://web.telegram.org/k/#{user_id}\">Пользователь</a> без юзернейма\n"));
                        }
                        UserIdOrUsername::Username(username) => {
                            text.push_str(&format!("🦍Пользователь : @{username}\n"));
                        }
                    }

                    match promo.kind {
                        crate::common::PromocodeKind::Sale => {
                            text.push_str(&format!(
                                "Активировал промокод: {} (-{}%)",
                                promo.text, promo.value
                            ));
                        }
                        crate::common::PromocodeKind::Balance => {
                            text.push_str(&format!(
                                "Активировал промокод: {} (+${} USD)",
                                promo.text, promo.value
                            ));
                        }
                    }
                }
                Notification::UserReferred { id, referral } => {
                    match id {
                        UserIdOrUsername::UserId(user_id) => {
                            text.push_str(&format!("🦍<a href=\"https://web.telegram.org/k/#{user_id}\">Пользователь</a> без юзернейма\n"));
                        }
                        UserIdOrUsername::Username(username) => {
                            text.push_str(&format!("🦍Пользователь : @{username}\n"));
                        }
                    }

                    text.push_str(&format!("Зашел по рефералке от {}", referral));
                }
                Notification::ModelAdded { .. } => {
                    // This case is handled separately in send_model_added method
                    unreachable!("ModelAdded notifications should be handled by send_model_added")
                }
            };

            // Get the channel ID from the notification config
            let channel_id = match db.get_notification_config(config_kind).await {
                Ok(Some(config)) => config.channel_id,
                _ => {
                    tracing::warn!(
                        "Failed to get notification config for kind: {:?}, using default channel",
                        config_kind
                    );
                    default_channel_id
                }
            };

            let json = serde_json::json!({
                "text": text,
                "parse_mode": "HTML",
                "chat_id": channel_id,
            });

            let response = client.post(url).json(&json).send().await.unwrap();
            if !response.status().is_success() {
                panic!("{}: {}", response.status(), response.text().await.unwrap());
            }
        });
    }

    fn send_model_added(&self, kind: Notification) {
        let client = self.client.clone();
        let bot_token = self.bot_token.clone();
        let default_channel_id = self.default_channel_id;
        let db = self.db.clone();

        tokio::spawn(async move {
            if let Notification::ModelAdded {
                product_id,
                model_name,
                description,
                medias,
                bot_username,
            } = kind
            {
                // Get the channel ID from the notification config
                let channel_id = match db
                    .get_notification_config(NotificationKind::ModelAdded)
                    .await
                {
                    Ok(Some(config)) => config.channel_id,
                    _ => {
                        tracing::warn!(
                            "Failed to get notification config for ModelAdded, using default channel"
                        );
                        default_channel_id
                    }
                };

                // Generate bot link to the product
                let bot_link = format!(
                    "https://t.me/{}?start=product_{}",
                    bot_username, product_id.0
                );

                // Prepare caption with description and link
                let caption = format!(
                    "{}\n\n\
                    🔗 <a href=\"{}\">Перейти к модели в боте</a>",
                    description, bot_link
                );

                if medias.is_empty() {
                    tracing::warn!(
                        "No media found for model {}, skipping channel post",
                        model_name
                    );
                    return;
                }

                // Get media file IDs from database
                let mut media_items = Vec::new();
                for (index, media) in medias.iter().enumerate() {
                    match db.get_media_file_id(&bot_username, media.name()).await {
                        Ok(Some(file_id)) => {
                            let media_type = match media {
                                crate::media::Media::Photo(_) => "photo",
                                crate::media::Media::Video(_) => "video",
                            };

                            let mut media_obj = serde_json::json!({
                                "type": media_type,
                                "media": file_id,
                            });

                            // Add caption only to the first media item
                            if index == 0 {
                                media_obj["caption"] = serde_json::Value::String(caption.clone());
                                media_obj["parse_mode"] =
                                    serde_json::Value::String("HTML".to_string());
                            }

                            media_items.push(media_obj);
                        }
                        Ok(None) => {
                            tracing::warn!("Media file ID not found for {}", media.name());
                        }
                        Err(e) => {
                            tracing::error!(
                                "Failed to get media file ID for {}: {}",
                                media.name(),
                                e
                            );
                        }
                    }
                }

                if media_items.is_empty() {
                    tracing::warn!(
                        "No valid media items found for model {}, skipping channel post",
                        model_name
                    );
                    return;
                }

                // Send media group to channel
                let url = format!("https://api.telegram.org/bot{}/sendMediaGroup", bot_token);
                let json = serde_json::json!({
                    "chat_id": channel_id,
                    "media": media_items,
                });

                let response = client.post(url).json(&json).send().await.unwrap();
                if !response.status().is_success() {
                    tracing::error!(
                        "Failed to send model to channel: {}: {}",
                        response.status(),
                        response.text().await.unwrap()
                    );
                } else {
                    tracing::info!("Successfully posted model {} to channel", model_name);
                }
            }
        });
    }
}
