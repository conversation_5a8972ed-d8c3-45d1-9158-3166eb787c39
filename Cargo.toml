[package]
name = "ded-tg-shop"
version = "0.1.0"
edition = "2021"

[lib]
name = "ded_tg_shop"
path = "src/lib.rs"

[dependencies]
teloxide = "0.15"

paste = "1"

serde = "1.0"
serde_json = "1.0"
serde_with = "3"

once_cell = "1"
itertools = "0.14"
uuid = { version = "1", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
color-eyre = "0.6"
humantime = "2"
urlencoding = "2"

tracing-subscriber = { version = "0.3", features = ["fmt", "json", "valuable", "env-filter"] }
tracing-loki = "0.2"

tracing = "0.1"
tokio = { version = "1", features = ["macros", "fs", "rt-multi-thread", "sync"] }
reqwest = { version = "0.12", features = ["json", "multipart"] }

typetag = "0.2"
nom = "8"
libsql = "0.9"
md5 = "0.7"
futures = "0.3"

moka = { version = "0.12", features = ["future", "sync"] }
dotenvy = "0.15"
regex = "1.11"
zip = "2"
tempfile = "3"

grammers-client = { path = "grammers/grammers-client", features = ["proxy", "parse_invite_link"] }
grammers-session = { path = "grammers/grammers-session" }
grammers-tl-types = { path = "grammers/grammers-tl-types" }
grammers-mtproto = { path = "grammers/grammers-mtproto" }
grammers-mtsender = { path = "grammers/grammers-mtsender" }

[dev-dependencies]
teloxide_tests = "0.3"
